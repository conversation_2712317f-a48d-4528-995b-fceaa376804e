<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>YimaWmsServer</title>

    <link rel="stylesheet"
          href="v3.18.2-dist/ol.css" type="text/css">
    <script src="v3.18.2-dist/ol.js"></script>
    <script src="common.js"></script>
    <script src="wmts-dimensions.js"></script>
    <script src="http://apps.bdimg.com/libs/jquery/1.6.4/jquery.js"></script>
    <style>
        button{
            width: 100px;
            height: 25px;
            font-size: 14px;
        }
        .big_box{
            display: flex;
        }
        .left_daohang{
            width: 10%;
            height: 100%;
            overflow: auto;
            max-height: 700px;
        }
        .item1{
            width: 100%;
            height: 65px;
            line-height: 65px;
            justify-content: space-between;
            cursor: pointer;
            text-align:center;
        }
        .item1:hover{
            background-color: #cccccc;
        }
        .item1_text{
            font-size: 22px;
            color: black;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .item1_item2{
            cursor: pointer;
            display: none;
        }
        .item2{
            width: 100%;
            height: 40px;
            line-height: 40px;
            text-align:center;
        }
        .item2:hover{
            background-color: #cccccc;
        }
        .item2_text{
            font-size: 16px;
            color: dimgray;
        }
        .content{
            width: 100%;
            height: 100%;
        }
        .map{
            width: 100%;
            height: 100%;

        }
        .bar{
            float: right;
            height: 0px;
        }
    </style>
</head>
<body>
<div class="big_box">
    <div class="left_daohang"> 

        <div class="item1">  
            <span class="item1_text">显示级别</span>
        </div>

        <div class="item1_item2" >
            <div class="item2" onclick="SetMapShowLevel(0)">
                <span class="item2_text">基本显示</span>
            </div>
            <div class="item2" onclick="SetMapShowLevel(1)">
                <span class="item2_text">标准显示</span>
            </div>
            <div class="item2" onclick="SetMapShowLevel(2)">
                <span class="item2_text">完全显示</span>
            </div>
        </div> 

        <div class="item1">  
            <span class="item1_text">颜色模式</span>
        </div>
        <div class="item1_item2" >
            <div class="item2" onclick="SetMapColorModel(1)">
                <span class="item2_text">白昼</span>
            </div>
            <div class="item2" onclick="SetMapColorModel(4)">
                <span class="item2_text">黄昏</span>
            </div>
            <div class="item2" onclick="SetMapColorModel(5)">
                <span class="item2_text">夜晚</span>
            </div>
        </div>

        <div class="item1">  
            <span class="item1_text">海图/陆图叠加</span>
        </div>
        <div class="item1_item2" >
            <div class="item2" onclick="SetShowLandMap(1)">
                <span class="item2_text">海图</span>
            </div>
            <div class="item2" onclick="SetShowLandMap(2)">
                <span class="item2_text">海图+卫星图</span>
            </div>
            <div class="item2" onclick="SetShowLandMap(3)">
                <span class="item2_text">海图+街道图</span>
            </div>
            <div class="item2" onclick="SetShowLandMap(4)">
                <span class="item2_text">卫星图</span>
            </div>
            <div class="item2" onclick="SetShowLandMap(5)">
                <span class="item2_text">街道图</span>
            </div> 
        </div>

        <div class="item1" onclick="GetLayerInfo()">
            <span class="item1_text">图层管理</span>
        </div>
        <div class="item1_item2">
            <div class="item2">
                <input type="text" placeholder="输入索引如41,70,80" id="layerPos" style="width: 90%; font-size: 9px;">
            </div>
            <div class="item2">
                <span class="item2_text" onclick="SetLayerShowOrNot(true)">显示</span>
            </div>
            <div class="item2">
                <span class="item2_text" onclick="SetLayerShowOrNot(false)">隐藏</span>
            </div> 
        </div> 

        <div class="item1">
            <span class="item1_text" onclick="GetMapLibInfo()">图库管理</span>
        </div>
        <div class="item1_item2">
            <div class="item2">
                <span class="item2_text">添加图幅</span>
            </div>
            <div class="item2">
                <span class="item2_text">删除图幅</span>
            </div>
        </div>

    </div>

    <div class="content"> 
        <div id="map-container" class="map"></div>
        <div class="bar">
            <span id="point"></span>
        </div>
        <div id="hintBar" style="height: 0"></div>
        <span id="theinfo"></span>
    </div>
</div>

<script>

    var map;
    var serverIP = "**********:8087"; //"localhost:8079"; //"*************:8079";//************
    var source;
    var thresholdVal = 1;
    var wmtsAccessId = 0;

    function StartWMTS(accessId){

        //地图格式 栅格
        var format = 'image/jpeg';
        //xy最大最小
        var bounds = [80.0, 20.0,
            160.0, 40.0];

        // 首先设置好WMTS瓦片地图的投影坐标系
        let projection = ol.proj.get('EPSG:3857');          // 获取web墨卡托投影坐标系
        let projectionExtent = projection.getExtent();      // web墨卡托投影坐标系的四至
        let width = ol.extent.getWidth(projectionExtent);   // web墨卡托投影坐标系的水平宽度，单位米
        let resolutions = [];                               // 瓦片分辨率
        let matrixIds = [];
        for(let z = 0; z < 14; z++){
            resolutions[z] = width / (256 * Math.pow(2, z)); 
            matrixIds[z] = z;
        }
        
        let wmtsTileGrid = new ol.tilegrid.WMTS({
            origin: ol.extent.getTopLeft(projectionExtent), // 原点（左上角）
            resolutions: resolutions,                       // 分辨率数组
            matrixIds: matrixIds                            // 矩阵ID，就是瓦片坐标系z维度各个层级的标识
        });  

        //栅格地图源
        source = new ol.source.WMTS({
            crossOrigin: null, 
            //url: "http://services.arcgisonline.com/arcgis/rest/services/Demographics/USA_Population_Density/MapServer/WMTS/", 
            //url: "http://t0.tianditu.com/ter_c/wmts",
            url: 'http://'+serverIP,
            matrixSet: 'EPSG:3857',             // 投影坐标系参数矩阵集
            format: 'image/png',                // 图片格式
            projection: projection,             // 投影坐标系            
            tileGrid: wmtsTileGrid,  // 投影坐标系
            style: 'styleByAccessId:' + accessId,
            dimensions: {
                'threshold': thresholdVal,
            },
        });

        //栅格图层
        var layers =  [new ol.layer.Tile({
            source: source
        })] 
       

        //投影
        // var projection = new ol.proj.Projection({
        //     code: 'EPSG:3857',
        //     units: 'm',
        //     axisOrientation: 'neu',
        //     global: false,
        // });

        var view = new ol.View({
            center: ol.proj.transform(
                [120.374798,36.07316], 'EPSG:4326', 'EPSG:3857'),
            projection: 'EPSG:3857',
            zoom:6
        }); 

        map = new ol.Map({
            layers: layers,
            target: 'map-container',
            view: view
        }); 

        $(map.getViewport()).mousedown(function(event){ 

            event.preventDefault();
            if(event.which == 3) {
                var coordinate = ol.proj.transform(map.getEventCoordinate(event), 'EPSG:3857', 'EPSG:4326'); 

                $('#hintBar').html("leftClick:"+coordinate[0]+" , "+coordinate[1]);
                SelectObjectByGeoPo(coordinate[0], coordinate[1]);
            }  
        });

        $(map.getViewport()).mousemove(function(event){
            event.preventDefault();  
            var coordinate = ol.proj.transform(map.getEventCoordinate(event), 'EPSG:3857', 'EPSG:4326');
           // $('#point').text(coordinate[0] + "° , " + coordinate[1] + "°  level:" + ); 
            $('#point').text(coordinate[0] + "° , " + coordinate[1]);
        });

        
    }

    function SetLayerShowOrNot(show) {        
            source.updateDimensions({'threshold': ++thresholdVal});
            var pos = document.getElementById('layerPos').value;
            console.log(pos)
            $.ajax({
                url : "http://"+serverIP+"/?REQUEST=SetLayerShowOrNot&accessId=" + wmtsAccessId + "&layerPos="+ pos +"&show=" + show + "",
                type : "Get",
                dataType : "html",
                scriptCharset: 'utf-8',
                xhrFields: {
                    withCredentials: true
                },
                crossDomain: true,
                error : function(data) {
                    //alert("查询失败")
                },
                success : function(data) {
                    $('#hintBar').append(data + "</br>");
                    Refresh();
                }
            });
        }

        function SetShowLandMap(showLandMapModel) { 
            source.updateDimensions({'threshold': ++thresholdVal});
            $('#hintBar').html(""); 

           $.ajax({
               url : "http://"+serverIP+"/?REQUEST=SetShowLandMapModel&accessId=" + wmtsAccessId + "&landMap=" + showLandMapModel,
               type : "Get",
               dataType : "html",
               scriptCharset: 'utf-8',
               xhrFields: {
                   withCredentials: true
               },
               crossDomain: true,  
               error : function(data) {
                   //Refresh();
               },
               success : function(data) { 
                   $('#hintBar').append(data + "</br>"); 
                   source.refresh();
                   Refresh();
               }
           }); 
        }
        

        function SetMapShowLevel(level) { 
            source.updateDimensions({'threshold': ++thresholdVal});

            $('#hintBar').html("");
            var displayLevel;
            switch (level)
            {
                case 0:
                    displayLevel = "DISPLAYBASE";
                    break;
                case 1:
                    displayLevel = "STANDARD";
                    break;
                case 2:
                    displayLevel = "OTHER";
                    break;
                default:
                    displayLevel = "STANDARD";
            }
            $.ajax({
                url : "http://"+serverIP+"/?REQUEST=SetMapShowLevel&accessId=" + wmtsAccessId + "&level="+ displayLevel,
                type : "Get",
                dataType : "html",
                scriptCharset: 'utf-8',
                xhrFields: {
                    withCredentials: true
                },
                crossDomain: true,
                error : function(data) {
                    //alert("查询失败")
                },
                success : function(data) {
                    $('#hintBar').append(data + "</br>");

                    source.set('style', '1');
                    source.refresh(); 

                    Refresh();
                }
            }); 
        } 


        function SetMapColorModel(model) {        
           // source.updateDimensions({'threshold': ++thresholdVal});
           $('#hintBar').html("");

           var colorModel;
           switch (model)
           {
               case 1:
                   colorModel = "DAY_BRIGHT";
                   break;
               case 4:
                   colorModel = "DUSK";
                   break;
               case 5:
                   colorModel = "DARK";
                   break;
               default:
                   colorModel = "DAY_BRIGHT";
           }

           $.ajax({
               url : "http://"+serverIP+"/?REQUEST=SetMapColorModel&accessId=" + wmtsAccessId + "&model=" + colorModel,
               type : "Get",
               dataType : "html",
               scriptCharset: 'utf-8',
               xhrFields: {
                   withCredentials: true
               },
               crossDomain: true,  
               error : function(data) {
                   //Refresh();
               },
               success : function(data) { 
                   $('#hintBar').append(data + "</br>"); 
                   source.refresh();
                   Refresh();
               }
           }); 
        }

        function SetShowMapFrame(show){        
            source.updateDimensions({'threshold': ++thresholdVal});
            $('#hintBar').html("");
            $.ajax({
                url : "http://"+serverIP+"/?REQUEST=SetShowMapFrame&accessId=" + wmtsAccessId + "&show=" + show,
                type : "Get",
                dataType : "html",
                scriptCharset: 'utf-8',
                xhrFields: {
                    withCredentials: true
                },
                crossDomain: true,
                error : function(data) {
                    //alert("查询失败")
                },
                success : function(data) {
                    $('#hintBar').append(data + "</br>");
                }
            });
        }

        //图片保存的模式(0=默认的切图模式、2=谷歌地图模式、3=arcgis、4=经纬直投天地图)
        function SetIfOverlayLandMap(mode){        
            source.updateDimensions({'threshold': ++thresholdVal});
            var imagePash = "H:\\google\\";
            $.ajax({
                url : "http://"+serverIP+"/?REQUEST=SetIfOverlayLandMap&mode=" + mode + "&imagePash=" + imagePash + "&LandMapName=0",
                type : "Get",
                dataType : "html",
                scriptCharset: 'utf-8',
                xhrFields: {
                    withCredentials: true
                },
                crossDomain: true,
                error : function(data) {
                    //alert("查询失败")
                },
                success : function(data) {
                    $('#hintBar').append(data + "</br>");
                }
            });
        }

        function Refresh(){
            // var params = layers[0].getSource().getParams();
            // params.t = new Date().getMilliseconds();
            // layers[0].getSource().updateParams(params); 
            source.updateDimensions({ time: new Date().getTime() });
        }


    $(document).ready(function(){
        $('.item1').click(
            function(){
                if($(this).next().is(":hidden")){
                    $(this).next().show(500);
                }
                else if($(this).next().children().length==0){}
                else{
                    $(this).next().hide(500)
                }
            }
        );

    }); 
    
    function OnGetAccessId(accssId) {
        StartWMTS(accssId);
    }
     

    $(window).load(function(){
    	$.ajax({
            url : "http://"+serverIP+"/?REQUEST=ClearUserTokenCookie",
            type : "Get",
            dataType : "json",
            scriptCharset: 'utf-8',
            xhrFields: {
                withCredentials: true
            },
            crossDomain: true,
            error : function(data) {
                //alert("查询失败")
            },
            success : function(data) {
                console.log("ClearUserTokenCookie returned...");
                OnGetAccessId(data);
                wmtsAccessId = data;
                console.log(data);
            }
        }); 
		 });  

    function GetMapLibInfo() {
        $('#hintBar').html("");
        $.ajax({
            url : "http://"+serverIP+"/?REQUEST=GetMapLib",
            type : "Get",
            dataType : "json",
            scriptCharset: 'utf-8',
            xhrFields: {
                withCredentials: true
            },
            crossDomain: true,
            error : function(data) {
                //alert("查询失败")
            },
            success : function(data) {
                data.forEach(function (map){
                    $('#hintBar').append(map.MapName + "\t" + map.leftDown + "\t" + map.rightUp + "<br>");
                })
            }
        });
    }

    function GetLayerInfo() {
        $('#hintBar').html("");
        $.ajax({
            url : "http://"+serverIP+"/?REQUEST=GetLayers",
            type : "Get",
            dataType : "json",
            scriptCharset: 'utf-8',
            xhrFields: {
                withCredentials: true
            },
            crossDomain: true,
            error : function(data) {
                //alert("查询失败")
            },
            success : function(data) {
                data.forEach(function (layer){
                    $('#hintBar').append(layer.layerPos + "\t" + layer.layerName + "\t" + layer.layerNameToken + "\t" + layer.show + "<br>");
                })
            }
        });
    }

    

    function SelectObjectByGeoPo(lon, lat){
        $('#hintBar').html("<span style='color:red'>鼠标点击选中:</span>");
        $.ajax({
            url : "http://"+serverIP+"/?REQUEST=SelectObjectByGeoPo&lon="+ lon+"&lat="+lat,
            type : "Get",
            dataType : "json",
            scriptCharset: 'utf-8',
            xhrFields: {
                withCredentials: true
            },
            crossDomain: true,
            error : function(data) {
                //alert("查询失败")
            },
            success : function(data) {
                data.forEach(function (obj) {
                    $('#hintBar').append("</br>" + obj.mapName + "\t" + obj.layerName + "\t" + obj.objectPos);
                })
            }
        });
    }
    
</script>

</body>
</html>